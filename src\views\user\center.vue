<template>
  <div class="flex flex-col h-screen">
    <QNavbar>
      <h1 class="text-42 text-primary font-semibold">个人中心</h1>
      <template #right>
        <div>
          <QButton
            size="small"
            type="primary"
            ghost
            @click="onToSetPassword"
          >
            设置密码</QButton
          >
        </div>
      </template>
    </QNavbar>
    <main class="flex-1 overflow-y-auto bg-[#E5EAFF] box-border">
      <div class="content mx-11 pt-10">
        <div class="bg-[#FFFFFFBF] rounded-20 py-7 px-15">
          <div class="text-32 text-primary font-semibold">我的资料</div>
          <div class="line my-7 bg-[#747EB254] h-[1px]"></div>
          <ul class="text-28 font-normal text-primary-description flex flex-col gap-10">
            <li class="flex items-center justify-between">
              <div class="label">姓名</div>
              <div class="text flex items-center gap-3 text-primary">
                <span>{{ userInfo?.display_name }}</span>
                <QTag
                  type="success"
                  class="border border-success flex items-center gap-1"
                  shape="round"
                  size="small"
                >
                  <svg
                    width="19"
                    height="19"
                    viewBox="0 0 19 19"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle
                      cx="7.55823"
                      cy="4.38586"
                      r="3.38586"
                      stroke="#32C59D"
                      stroke-width="2"
                    />
                    <path
                      d="M1.35454 17.0096C-0.22987 12.3832 3.78294 8.25195 7.58383 8.25195C10.0787 8.25195 12.2634 9.57944 13.4703 11.5666"
                      stroke="#32C59D"
                      stroke-width="2"
                      stroke-linecap="round"
                    />
                    <path
                      d="M8.64111 14.0295L11.9366 17.325L17.07 12.3184"
                      stroke="#32C59D"
                      stroke-width="2"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>

                  已实名
                </QTag>
              </div>
            </li>
            <li class="flex items-center justify-between">
              <div class="label">手机号码</div>
              <div class="text text-primary">{{ maskedMobile }}</div>
            </li>
            <!-- <li class="flex items-center justify-between">
              <div class="label">户籍所在地</div>
              <div class="text text-primary">{{}}</div>
            </li> -->
          </ul>
        </div>
        <div
          v-if="userInfo?.wechat_bind"
          class="bg-[#FFFFFFBF] rounded-20 py-7 px-15 mt-8"
        >
          <div class="text-32 text-primary font-semibold">关联微信</div>
          <div class="line my-7 bg-[#747EB254] h-[1px]"></div>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <img
                src="@/assets/wx-icon.svg"
                width="48"
                height="48"
                alt=""
              />
              <div class="text-28 text-primary-description">微信昵称 （halo_sifon）</div>
            </div>
          </div>
        </div>

        <div class="bg-[#FFFFFFBF] rounded-20 py-7 px-15 mt-8">
          <div class="text-32 text-primary font-semibold">
            <template v-if="APP_ENV === 'anlu'">我的社区</template>
            <template v-else-if="APP_ENV === 'xiaogan'">我的网格</template>
            <template v-else>我的社区</template>
          </div>
          <div class="line my-7 bg-[#747EB254] h-[1px]"></div>
          <div class="grid grid-cols-1 gap-3">
            <div
              v-for="(item, index) in sceneList"
              :key="item.key"
              class="flex items-center justify-between rounded-2xl p-5 cursor-pointer transition-colors gap-5"
              @click="switchGridRegion(item)"
            >
              <div class="text-28 text-primary-description min-w-0 truncate">
                {{ item.label }}
                <span
                  v-if="index === 0"
                  class="text-info"
                  >当前社区</span
                >
              </div>
              <div
                v-if="item.key === currentSceneData?.key"
                class="shrink-0"
              >
                <svg
                  width="42"
                  height="27"
                  viewBox="0 0 42 27"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M2.8916 10L15.7891 24.4647L39.5012 2.75928"
                    stroke="#32C59D"
                    stroke-width="4"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-[#FFFFFFBF] rounded-20 py-7 px-15 mt-8">
          <div class="text-32 text-primary font-semibold flex items-center justify-between">
            我的社群
          </div>
          <div class="line my-7 bg-[#747EB254] h-[1px]"></div>

          <ul
            v-if="!wxCommunityLoading && myWxCommunity?.length"
            class="flex flex-col gap-7"
          >
            <li
              v-for="item in myWxCommunity"
              :key="item.wxq_name"
              class="bg-[#E5EAFF80] rounded-2xl"
            >
              <div class="header p-5 flex items-center gap-2 w-full border-b border-[#747EB254]">
                <img
                  src="@/assets/avatar.svg"
                  width="48"
                  height="48"
                  alt=""
                />
                <div class="text-28 text-primary tracking-tighter font-semibold">
                  {{ item.wxq_name }}
                </div>
              </div>
              <div class="body px-20">
                <div class="flex items-center justify-between py-8">
                  <div class="item flex flex-col items-center gap-0.5">
                    <div class="num tracking-tighter text-4xl font-medium text-info">
                      {{ formatNumberWithComma(item.wxq_member_count) }}
                    </div>
                    <div class="lable text-2xl text-primary-description">居民人数</div>
                  </div>
                  <div class="item flex flex-col items-center gap-0.5">
                    <div class="num tracking-tighter text-4xl font-medium text-warning">
                      {{ formatNumberWithComma(item.wxq_new_member_count) }}
                    </div>
                    <div class="lable text-2xl text-primary-description">今日新增</div>
                  </div>
                  <div class="item flex flex-col items-center gap-0.5">
                    <div class="num tracking-tighter text-4xl font-medium text-success">
                      {{ formatNumberWithComma(item.wxq_new_member_count_7) }}
                    </div>
                    <div class="lable text-2xl text-primary-description">近7日新增</div>
                  </div>
                </div>
              </div>
            </li>
          </ul>

          <SuperEmpty v-if="!wxCommunityLoading && !myWxCommunity?.length"></SuperEmpty>
        </div>

        <div
          class="bg-[#FFFFFFBF] rounded-20 py-7 px-15 mt-8 text-32 text-danger text-center"
          @click="onLogout"
        >
          退出登录
        </div>

        <div class="h-16"></div>
      </div>
    </main>
  </div>
</template>
<script setup lang="ts">
  import { QButton, QMessageBox, QNavbar, QTag } from '@/components/q-components'
  import { SuperEmpty } from '@/components/super-element'
  import { routeNameMap } from '@/router/route-name'
  import { useSceneStore } from '@/stores/scene'
  import { useUserStore } from '@/stores/user'
  import { formatNumberWithComma, getAppEnv } from '@/utils'
  import { encryptPhone } from '@/utils/formart'
  import { sdk } from '@/utils/sdk'
  import { storeToRefs } from 'pinia'
  import { computed, onMounted } from 'vue'
  import { useRequest } from 'vue-request'
  import { useRouter } from 'vue-router'
  import { SceneTypes } from 'uniplat-sdk'

  const APP_ENV = getAppEnv()
  const userStore = useUserStore()
  const sceneStore = useSceneStore()

  const router = useRouter()
  const { userInfo } = storeToRefs(userStore)
  const { currentSceneData, sceneList } = storeToRefs(sceneStore)

  const maskedMobile = computed(() => {
    return encryptPhone(userInfo.value?.mobile)
  })

  const switchGridRegion = async (item: SceneTypes.SceneData) => {
    await sceneStore.switchGridRegion(item)
  }

  const onToSetPassword = () => {
    router.push({
      name: routeNameMap.SetPassword
    })
  }

  const onLogout = async () => {
    const action = await QMessageBox.confirm({
      title: '提示',
      message: '确定退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })

    if (action === 'confirm') {
      // 使用统一的退出登录方法
      const { logout } = await import('@/utils/logout')
      await logout()
    }
  }

  type MyWxCommunity = {
    wxq_name: string
    qr_code: string
    qr_code2: string
    wxq_member_count: string
    wxq_new_member_count: string
    wxq_new_member_count_7: string
  }
  /**
   * 接口文档
   * @see https://teammix.yuque.com/ka8cpo/cgkf5h/mr2af3suit5lwac5#wnmfb
   */
  const { data: myWxCommunity, loading: wxCommunityLoading } = useRequest(() =>
    sdk.uniplatSdk
      .domainService('wecom_core', 'client_api', 'getMyWxCommunity')
      .post<MyWxCommunity[]>()
  )

  onMounted(async () => {
    // 如果场景列表还没有加载，先加载
    if (!sceneStore.sceneList) {
      await sceneStore.fetchSceneList()
    }
  })

  defineOptions({
    name: 'UserCenter'
  })
</script>
<style scoped></style>
